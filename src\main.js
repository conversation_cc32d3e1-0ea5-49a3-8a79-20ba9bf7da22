import './style.css'
import { AuthForm } from './js/components/AuthForm.js'

// Initialize the application
class App {
  constructor() {
    this.init();
  }

  init() {
    this.setupDOM();
    this.initializeAuth();
  }

  setupDOM() {
    document.querySelector('#app').innerHTML = `
      <div class="min-h-screen bg-gray-100 py-8">
        <div class="container mx-auto px-4">
          <header class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">Auth System</h1>
            <p class="text-gray-600 mt-2">Vanilla JS + Vite + Tailwind CSS</p>
          </header>

          <main id="authContainer">
            <!-- Auth forms will be rendered here -->
          </main>
        </div>
      </div>
    `;
  }

  initializeAuth() {
    this.authForm = new AuthForm('authContainer');
    this.authForm.checkAuthState();
  }
}

// Start the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new App();
});
